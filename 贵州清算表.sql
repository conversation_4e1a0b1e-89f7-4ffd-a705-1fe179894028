create table shebao_guizhou_settle_of_month
(
    id               bigint(64)                                    not null
        primary key,
    chain_id         varchar(32)                                   null comment '连锁id',
    clinic_id        varchar(32)                                   null comment '诊所id',
    region           varchar(32)                                   null comment '区域',
    yae366           varchar(32)                                   null comment '清算期号',
    ykb065           varchar(8)                                    null comment '执行社会保险办法',
    yka316           varchar(8)                                    null comment '清算类别',
    ykb037           varchar(8)                                    null comment '清算分中心',
    yka055           decimal(16, 2)                                null comment '费用总额',
    yka248           decimal(16, 2)                                null comment '基本医疗统筹支付金额',
    yka062           decimal(16, 2)                                null comment '大额医疗支付金额',
    yka063           decimal(16, 2)                                null comment '公务员统筹支付金额',
    yka065           decimal(16, 2)                                null comment '个人帐户支付金额',
    psn_cash_pay     decimal(16, 2)                                null comment '现金支付金额',
    apply_ykc179     varchar(32)                                   null comment '清算申请人',
    apply_yke150     varchar(32)                                   null comment '清算申请时间',
    revoke_ykc179    varchar(32)                                   null comment '清算撤销申请人',
    revoke_yke150    varchar(32)                                   null comment '清算撤销申请时间',
    ykb053           varchar(32)                                   null comment '清算申请流水号',
    status           tinyint(1) unsigned default 0                 not null comment '清算状态 0=正常 1=撤销',
    is_deleted       tinyint(1) unsigned default 0                 not null comment '是否删除状态 0=正常 1=已删除',
    created          timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                                   null comment '创建人',
    last_modified    timestamp           default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by varchar(32)                                   null comment '修改人'
)
    comment '贵州清算表';

create index idx_chainid_clinicid
    on shebao_guizhou_settle_of_month (chain_id, clinic_id);