create table shebao_guizhou_dict
(
    id        bigint(64)     not null
        primary key,
    region    varchar(64)    null comment '区域',
    yka002    varchar(64)    null comment '医保通用项目编码',
    yka003    varchar(256)   null comment '医保通用项目名称',
    yae036    varchar(32)    null comment '变更时间',
    yka001    varchar(8)     null comment '大类编码',
    ykd110    varchar(8)     null comment '统计类型',
    yka389    varchar(256)   null comment '拼音助记码',
    yka401    varchar(256)   null comment '五笔助记码',
    yka295    varchar(32)    null comment '最小计价单位',
    aka074    text           null comment '规格',
    aka070    varchar(64)    null comment '剂型名称',
    yaa027    varchar(64)    null comment '物价编码',
    yka007    varchar(256)   null comment '批准文号',
    yke100    text           null comment '项目内涵',
    yke054    text           null comment '除外内容',
    yke103    text           null comment '项目说明',
    aae013    text           null comment '备注',
    yka096    decimal(15, 2) null comment '自付比例',
    yae375    varchar(8)     null comment '目录特项使用标志',
    yka284    varchar(256)   null comment '国家医保目录编码',
    yka233    decimal(16, 6) null comment '招标价格',
    yka601    varchar(256)   null comment '生产厂家',
    yka603    varchar(8)     null comment '新老目录标志',
    yka604    varchar(8)     null comment '发票类别编号',
    yka606    varchar(256)   null comment '批准文号备注',
    yka609    text           null comment '商品名',
    yka459    decimal(16, 6) null comment '职工最高限价',
    yka096_lx decimal(5, 4)  null comment '离休自付比例',
    yka096_jm decimal(5, 4)  null comment '居民自付比例',
    yka459_lx decimal(16, 6) null comment '离休最高限价',
    yka459_jm decimal(16, 6) null comment '居民最高限价',
    yka096_tj decimal(5, 4)  null comment '生育自付比例',
    yka459_tj decimal(16, 6) null comment '生育最高限价',
    aka101    varchar(8)     null comment '医院等级',
    yka096_gs decimal(5, 4)  null comment '工伤自付比例',
    yka459_gs decimal(16, 6) null comment '工伤最高限价',
    yka828    varchar(8)     null comment '材料来源(1 国产 2进口）',
    yka826    varchar(8)     null comment '是否民族药(0 否 1是)',
    yka829    varchar(8)     null comment '是否单独收费(0 否 1 是)',
    yka751    varchar(8)     null comment '公立医院改革标识(0 否 1 是)',
    ykl824    text           null comment '工伤项目内涵',
    yka430    varchar(8)     null comment '生育项目标志',
    yka431    varchar(8)     null comment '工伤项目标志',
    aaalsh    varchar(64)    null comment '下载流水号',
    aae030    timestamp      null comment '目录启用时间',
    aae031    timestamp      null comment '目录停用时间',
    yka459_cg decimal(16, 6) null comment '采购药限价',
    yka459_lb decimal(16, 6) null comment '居民两病目录限价'
);

create index shebao_guizhou_dict_yka002_index
    on shebao_guizhou_dict (yka002);

create index shebao_guizhou_dict_yka284_index
    on shebao_guizhou_dict (yka284);



create table shebao_guizhou_payment_result
(
    id                    bigint(64)                           not null
        primary key,
    chain_id              varchar(32)                          null comment '连锁id',
    clinic_id             varchar(32)                          null comment '诊所id',
    task_id               varchar(64)                          null comment '交易任务id',
    region                varchar(32)                          null comment '区域',
    is_refunded           tinyint(1) default 0                 not null comment '是否已退费，0 未退费；1 已退费',
    doctor_id             varchar(32)                          null comment '医生id',
    doctor_name           varchar(64)                          null comment '医生名称',
    patient_id            varchar(32)                          null comment '病人id',
    department_id         varchar(32)                          null comment '科室id',
    department_name       varchar(64)                          null comment '科室名称',
    relation_to_patient   varchar(64)                          null comment '本次刷卡的持卡人和患者的关系',
    card_id               varchar(64)                          null comment '社保卡号',
    ykc173                varchar(256)                         null comment '门诊诊断信息',
    hisfyze               decimal(16, 2)                       null comment 'HIS费用总额',
    aka130                varchar(8)                           null comment '支付类别',
    yka110                varchar(32)                          null comment '发票号',
    aae013                varchar(256)                         null comment '备注',
    aae011                varchar(32)                          null comment '经办人',
    ykc141                varchar(64)                          null comment '经办人姓名',
    ykb065                varchar(8)                           null comment '执行社会保险办法',
    akc190                varchar(32)                          null comment '就诊编号',
    yab003                varchar(8)                           null comment '分中心编号',
    yka103                varchar(32)                          null comment '结算编号',
    aac001                varchar(32)                          null comment '个人编号',
    yka065                decimal(16, 2)                       null comment '个人帐户支付金额',
    aae036                varchar(32)                          null comment '经办时间',
    yka055                decimal(16, 2)                       null comment '医保结算费用总额',
    yka056                decimal(16, 2)                       null comment '全自费金额',
    yka057                decimal(16, 2)                       null comment '挂钩自付金额',
    yka111                decimal(16, 2)                       null comment '符合范围金额',
    yka058                decimal(16, 2)                       null comment '进入起付线金额',
    yka248                decimal(16, 2)                       null comment '基本医疗统筹支付金',
    yka062                decimal(16, 2)                       null comment '大额医疗支付金额 ',
    yke030                decimal(16, 2)                       null comment '公务员补助报销金额',
    ake032                decimal(16, 2)                       null comment '城乡居民卫计局补偿金额（卫计补偿）',
    ake181                decimal(16, 2)                       null comment '医疗救助补偿金额',
    ake173                decimal(16, 2)                       null comment '其他基金支付',
    akc087                decimal(16, 2)                       null comment '本次个人帐户支付后帐户余额',
    ykb037                varchar(8)                           null comment '清算分中心',
    yka316                varchar(8)                           null comment '清算类型',
    akc021                varchar(8)                           null comment '医疗人员类型',
    ykc120                varchar(8)                           null comment '公务员级别',
    yab139                varchar(8)                           null comment '参保所属分中心',
    aac003                varchar(64)                          null comment '姓名',
    aac004                varchar(8)                           null comment '性别 1男 2女 9无',
    aac002                varchar(64)                          null comment '公民身份证号',
    aac006                varchar(32)                          null comment '出生日期',
    akc023                decimal(4, 1)                        null comment '实足年龄',
    aab001                varchar(64)                          null comment '单位编码',
    aab004                varchar(640)                         null comment '单位名称',
    aac031                varchar(8)                           null comment '个人参保状态',
    ykc280                varchar(8)                           null comment '居民医疗人员类别',
    ykc281                varchar(8)                           null comment '居民医疗人员身份',
    yka054                varchar(8)                           null comment '清算方式',
    yae366                varchar(8)                           null comment '清算期号',
    akc090                decimal(6)                           null comment '本年真实住院次数',
    yka120                decimal(16, 2)                       null comment '基本统筹已累计金额',
    yka122                decimal(16, 2)                       null comment '大额统筹已累计金额',
    yka368                decimal(16, 2)                       null comment '公务员补助普通门诊起付年度累计(含慢性病)',
    yke025                decimal(16, 2)                       null comment '本年公务员门诊补助累计额(含慢性病)',
    yka900                decimal(16, 2)                       null comment '规定病种起付线累计',
    aae001                decimal(4)                           null comment '年度',
    yka083                decimal(16, 2)                       null comment '大病额外报销金额',
    yka084                decimal(16, 2)                       null comment '公务员额外报销金额',
    yka085                decimal(16, 2)                       null comment '工会额外报销金额',
    yka086                decimal(16, 2)                       null comment '尽快自付金额',
    yka087                decimal(16, 2)                       null comment '诊疗超标自付金额',
    yka088                decimal(16, 2)                       null comment '床位超标自付金额',
    yka090                decimal(16, 2)                       null comment '限价材料超标自付金额',
    yka089                varchar(32)                          null comment '单病种(结算)编码',
    yka027                varchar(640)                         null comment '单病种(结算)病种名称',
    yka028                decimal(16, 2)                       null comment '单病种(结算)医疗机构自费费用',
    yka345                decimal(16, 2)                       null comment '单病种(结算)包干标准',
    yka525                decimal(16, 2)                       null comment '医疗结算费用总,贵阳市医保接口返回',
    yka501                varchar(64)                          null comment '门诊产前补助',
    ykd092                varchar(64)                          null comment '重大疾病标识',
    yka902                varchar(64)                          null comment '慢性病门诊预设线累计',
    yka903                varchar(64)                          null comment '慢性病门诊补助年度累计',
    yka119                varchar(64)                          null comment '已使用额度',
    psn_cash_pay          decimal(14, 2)                       null comment '本次现金支付',
    hosp_part_amt         decimal(14, 2)                       null comment '医院承担',
    jybh                  varchar(32)                          null comment '交易编号',
    jylsh                 varchar(32)                          null comment '交易流水号',
    jyyzm                 varchar(64)                          null comment '交易验证码',
    is_confirmed          tinyint    default 0                 not null comment '医保中心是否已确认 0未确认 1已确认',
    setl_list_id          varchar(64)                          null comment '结算清单上传流水号',
    setl_list_upload_time varchar(32)                          null comment '结算清单上传时间',
    stmt_rslt             varchar(8)                           null comment '和社保的对账结果，0=平 1=不平 101=中心多 102=机构多 103=数据不一致',
    stmt_rslt_memo        text                                 null comment '和社保的对账结果失败后说明',
    is_deleted            tinyint(1) default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created               timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(32)                          null comment '创建人',
    last_modified         timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by      varchar(32)                          null comment '修改人'
)
    comment '贵州医保支付结果表';

create index idx_chain_id
    on shebao_guizhou_payment_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_payment_result (clinic_id);

create index idx_jylsh
    on shebao_guizhou_payment_result (jylsh);

create index idx_task_id
    on shebao_guizhou_payment_result (task_id);

create index idx_yka103
    on shebao_guizhou_payment_result (yka103);



create table shebao_guizhou_payment_result_item
(
    id                bigint(64)                           not null
        primary key,
    chain_id          varchar(32)                          null comment '连锁id',
    clinic_id         varchar(32)                          null comment '诊所id',
    payment_result_id bigint(64)                           null comment '支付结果id',
    goods_id          varchar(64)                          null comment '药品id',
    product_type      tinyint(1)                           null comment '药品类型',
    product_sub_type  tinyint(1)                           null comment '药品子类型',
    is_selected       tinyint                              null comment '是否选中',
    yka105            varchar(64)                          null comment '记账流水号',
    ykd125            varchar(64)                          null comment '医院项目流水号',
    ykd126            varchar(256)                         null comment '医院项目名称',
    yka002            varchar(64)                          null comment '医保通用项目编码',
    yka003            varchar(256)                         null comment '医保通用项目名称',
    akc226            decimal(16, 4)                       null comment '数量',
    akc225            decimal(16, 6)                       null comment '实际价格',
    yka315            decimal(16, 2)                       null comment '明细项目费用总额',
    yka097            varchar(64)                          null comment '开单科室编码',
    yka098            varchar(128)                         null comment '开单科室名称',
    ykd102            varchar(64)                          null comment '开单医生医师资格证号',
    ykd103            varchar(64)                          null comment '国家医保医生编码',
    yka099            varchar(64)                          null comment '开单医生姓名',
    yka100            varchar(64)                          null comment '受单科室编码',
    yka101            varchar(128)                         null comment '受单科室名称',
    ykd106            varchar(64)                          null comment '受单医生编码',
    yka102            varchar(64)                          null comment '受单医生姓名',
    yke123            varchar(32)                          null comment '明细发生时间',
    ykc141            varchar(64)                          null comment '经办人姓名',
    aae036            varchar(32)                          null comment '经办时间',
    aae013            varchar(256)                         null comment '备注',
    akc229            varchar(8)                           null comment '慢性病用药天数',
    yke201            varchar(128)                         null comment '中药使用方式',
    yka295            varchar(8)                           null comment '最小几计价单位',
    aka074            varchar(64)                          null comment '规格',
    aka070            varchar(64)                          null comment '剂型',
    yae374            varchar(64)                          null comment '剂型名称',
    yke009            varchar(8)                           null comment '是否医院制剂',
    yke186            varchar(8)                           null comment '医院审批标志',
    yka821            varchar(8)                           null comment '药品报销标志',
    yka830            varchar(64)                          null comment '国家目录编码',
    yka831            varchar(256)                         null comment '国家目录名称',
    unit              varchar(32)                          null comment '单位',
    is_deleted        tinyint(1) default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created           timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by        varchar(32)                          null comment '创建人',
    last_modified     timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by  varchar(32)                          null comment '修改人'
)
    comment '贵州结算明细表';

create index idx_chain_id
    on shebao_guizhou_payment_result_item (chain_id);

create index idx_clinic_id
    on shebao_guizhou_payment_result_item (clinic_id);

create index idx_payment_result_id
    on shebao_guizhou_payment_result_item (payment_result_id);

create index idx_yka002
    on shebao_guizhou_payment_result_item (yka002);

create index idx_yka830
    on shebao_guizhou_payment_result_item (yka830);



create table shebao_guizhou_pre_payment_result
(
    id                  bigint(64)                                    not null,
    chain_id            varchar(32)                                   null comment '连锁id',
    clinic_id           varchar(32)                                   null comment '诊所id',
    task_id             varchar(64)                                   null comment '交易任务id',
    region              varchar(32)                                   null comment '区域',
    relation_to_patient varchar(64)                                   null comment '本次刷卡的持卡人和患者的关系',
    card_id             varchar(64)                                   null comment '社保卡号',
    ykc173              varchar(256)                                  null comment '门诊诊断信息',
    hisfyze             decimal(16, 2)                                null comment 'HIS费用总额',
    aka130              varchar(8)                                    null comment '支付类别',
    yka110              varchar(32)                                   null comment '发票号',
    aae013              varchar(256)                                  null comment '备注',
    aae011              varchar(32)                                   null comment '经办人',
    ykc141              varchar(64)                                   null comment '经办人姓名',
    ykb065              varchar(8)                                    null comment '执行社会保险办法',
    akc190              varchar(32)                                   null comment '就诊编号',
    yka103              varchar(32)                                   null comment '结算编号',
    yab003              varchar(8)                                    null comment '分中心编号',
    aac001              varchar(32)                                   null comment '个人编号',
    yka065              decimal(16, 2)                                null comment '个人帐户支付金额',
    aae036              varchar(32)                                   null comment '经办时间',
    yka055              decimal(16, 2)                                null comment '医保结算费用总额',
    yka056              decimal(16, 2)                                null comment '全自费金额',
    yka057              decimal(16, 2)                                null comment '挂钩自付金额',
    yka111              decimal(16, 2)                                null comment '符合范围金额',
    yka058              decimal(16, 2)                                null comment '进入起付线金额',
    yka248              decimal(16, 2)                                null comment '基本医疗统筹支付金',
    yka062              decimal(16, 2)                                null comment '大额医疗支付金额 ',
    yke030              decimal(16, 2)                                null comment '公务员补助报销金额',
    ake032              decimal(16, 2)                                null comment '城乡居民卫计局补偿金额（卫计补偿）',
    ake181              decimal(16, 2)                                null comment '医疗救助补偿金额',
    ake173              decimal(16, 2)                                null comment '其他基金支付',
    akc087              decimal(16, 2)                                null comment '本次个人帐户支付后帐户余额',
    ykb037              varchar(8)                                    null comment '清算分中心',
    yka316              varchar(8)                                    null comment '清算类型',
    akc021              varchar(8)                                    null comment '医疗人员类型',
    ykc120              varchar(8)                                    null comment '公务员级别',
    yab139              varchar(8)                                    null comment '参保所属分中心',
    aac003              varchar(64)                                   null comment '姓名',
    aac004              varchar(8)                                    null comment '性别 1男 2女 9无',
    aac002              varchar(64)                                   null comment '公民身份证号',
    aac006              varchar(32)                                   null comment '出生日期',
    akc023              decimal(4, 1)                                 null comment '实足年龄',
    aab001              varchar(64)                                   null comment '单位编码',
    aab004              varchar(640)                                  null comment '单位名称',
    aac031              varchar(8)                                    null comment '个人参保状态',
    ykc280              varchar(8)                                    null comment '居民医疗人员类别',
    ykc281              varchar(8)                                    null comment '居民医疗人员身份',
    yka054              varchar(8)                                    null comment '清算方式',
    yae366              varchar(8)                                    null comment '清算期号',
    akc090              decimal(6)                                    null comment '本年真实住院次数',
    yka120              decimal(16, 2)                                null comment '基本统筹已累计金额',
    yka122              decimal(16, 2)                                null comment '大额统筹已累计金额',
    yka368              decimal(16, 2)                                null comment '公务员补助普通门诊起付年度累计(含慢性病)',
    yke025              decimal(16, 2)                                null comment '本年公务员门诊补助累计额(含慢性病)',
    yka900              decimal(16, 2)                                null comment '规定病种起付线累计',
    aae001              decimal(4)                                    null comment '年度',
    yka083              decimal(16, 2)                                null comment '大病额外报销金额',
    yka084              decimal(16, 2)                                null comment '公务员额外报销金额',
    yka085              decimal(16, 2)                                null comment '工会额外报销金额',
    yka086              decimal(16, 2)                                null comment '尽快自付金额',
    yka087              decimal(16, 2)                                null comment '诊疗超标自付金额',
    yka088              decimal(16, 2)                                null comment '床位超标自付金额',
    yka090              decimal(16, 2)                                null comment '限价材料超标自付金额',
    yka089              varchar(32)                                   null comment '单病种(结算)编码',
    yka027              varchar(640)                                  null comment '单病种(结算)病种名称',
    yka028              decimal(16, 2)                                null comment '单病种(结算)医疗机构自费费用',
    yka345              decimal(16, 2)                                null comment '单病种(结算)包干标准',
    yka525              decimal(16, 2)                                null comment '医疗结算费用总,贵阳市医保接口返回',
    yka501              varchar(64)                                   null comment '门诊产前补助',
    ykd092              varchar(64)                                   null comment '重大疾病标识',
    yka902              varchar(64)                                   null comment '慢性病门诊预设线累计',
    yka903              varchar(64)                                   null comment '慢性病门诊补助年度累计',
    yka119              varchar(64)                                   null comment '已使用额度',
    psn_cash_pay        decimal(14, 2)                                null comment '本次现金支付',
    hosp_part_amt       decimal(14, 2)                                null comment '医院承担',
    msgid               varchar(64)                                   null comment '对账时返回msgid,用于冲正',
    jybh                varchar(32)                                   null comment '交易编号',
    jylsh               varchar(32)                                   null comment '交易流水号',
    jyyzm               varchar(64)                                   null comment '交易验证码',
    is_cancelled        tinyint(1) unsigned default 0                 not null comment '是否取消了支付 0未取消, 1取消',
    has_reversed        tinyint(1) unsigned default 0                 not null comment '是否已经冲正过 0未冲正 1已冲正',
    reversed_time       varchar(32)                                   null comment '冲正时间',
    is_deleted          tinyint(1)          default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created             timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by          varchar(32)                                   null comment '创建人',
    last_modified       timestamp           default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by    varchar(32)                                   null comment '修改人'
)
    comment '贵州医保预结算表';

create index idx_chain_id
    on shebao_guizhou_pre_payment_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_pre_payment_result (clinic_id);

create index idx_jylsh
    on shebao_guizhou_pre_payment_result (jylsh);

create index idx_task_id
    on shebao_guizhou_pre_payment_result (task_id);



create table shebao_guizhou_pre_refund_result
(
    id               bigint(64)                           not null
        primary key,
    chain_id         varchar(32)                          null comment '连锁id',
    clinic_id        varchar(32)                          null comment '诊所id',
    task_id          varchar(64)                          null comment '交易任务id',
    region           varchar(64)                          null comment '区域',
    akc190           varchar(64)                          null comment '就诊编号',
    yab003           varchar(8)                           null comment '分中心编码',
    aka130           varchar(8)                           null comment '支付类别',
    yka103           varchar(64)                          null comment '结算编号',
    aae011           varchar(128)                         null comment '经办人编码',
    ykc141           varchar(64)                          null comment '经办人姓名',
    aae036           varchar(32)                          null comment '经办时间',
    aae013           varchar(256)                         null comment '退费原因',
    ykb065           varchar(8)                           null comment '社会保险办法',
    aac001           varchar(64)                          null comment '个人编码',
    jylsh            varchar(64)                          null comment '交易流水号',
    jyyzm            varchar(32)                          null comment '交易验证码',
    ojylsh           varchar(64)                          null comment '结算交易流水号',
    msgid            varchar(64)                          null comment '对账时返回msgid,用于冲正',
    is_canceled      tinyint(1) default 0                 not null comment '预退费是否关闭',
    has_reversed     tinyint(1) default 0                 not null comment '是否已经冲正过 0未冲正 1已冲正',
    reversed_time    varchar(32)                          null comment '冲正时间',
    is_deleted       tinyint(1) default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created          timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                          null comment '创建人',
    last_modified    timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by varchar(32)                          null comment '修改人'
)
    comment '贵州预退费表';

create index idx_chain_id
    on shebao_guizhou_pre_refund_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_pre_refund_result (clinic_id);

create index idx_task_id
    on shebao_guizhou_pre_refund_result (task_id);

create index idx_yka103
    on shebao_guizhou_pre_refund_result (yka103);



create table shebao_guizhou_refund_result
(
    id               bigint(64)                                    not null
        primary key,
    chain_id         varchar(32)                                   null comment '连锁id',
    clinic_id        varchar(32)                                   null comment '诊所id',
    task_id          varchar(64)                                   null comment '交易任务id',
    region           varchar(64)                                   null comment '区域',
    akc190           varchar(64)                                   null comment '就诊编号',
    yab003           varchar(8)                                    null comment '分中心编码',
    aka130           varchar(8)                                    null comment '支付类别',
    yka103           varchar(64)                                   null comment '结算编号',
    aae011           varchar(128)                                  null comment '经办人编码',
    ykc141           varchar(64)                                   null comment '经办人姓名',
    aae036           varchar(32)                                   null comment '经办时间',
    aae013           varchar(256)                                  null comment '退费原因',
    ykb065           varchar(8)                                    null comment '社会保险办法',
    aac001           varchar(64)                                   null comment '个人编码',
    jylsh            varchar(64)                                   null comment '交易流水号',
    jyyzm            varchar(32)                                   null comment '交易验证码',
    is_confirmed     tinyint(1) unsigned default 0                 not null comment '医保中心是否已确认 0未确认 1已确认',
    stmt_rslt        varchar(8)                                    null comment '和社保的对账结果，0=平 1=不平 101=中心多 102=机构多 103=数据不一致',
    stmt_rslt_memo   text                                          null comment '和社保的对账结果失败后说明',
    is_deleted       tinyint(1)          default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created          timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                                   null comment '创建人',
    last_modified    timestamp           default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by varchar(32)                                   null comment '修改人'
)
    comment '贵州退费表';

create index idx_chain_id
    on shebao_guizhou_refund_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_refund_result (clinic_id);

create index idx_task_id
    on shebao_guizhou_refund_result (task_id);

create index idx_yka103
    on shebao_guizhou_refund_result (yka103);



create table shebao_guizhou_settle_day
(
    id               bigint(64)                                    not null
        primary key,
    chain_id         varchar(32)                                   null comment '连锁id',
    clinic_id        varchar(32)                                   null comment '诊所id',
    region           varchar(64)                                   null comment '区域',
    settle_date      varchar(32)                                   null comment '对账日期',
    abc_total_fee    decimal(16, 2)                                null comment 'ABC支付金额',
    shebao_total_fee decimal(16, 2)                                null comment '社保支付金额',
    status           tinyint(1) unsigned default 0                 not null comment '状态（0：未对账；1：对账成功；2：对账失败）',
    stmt_rslt_dscr   text                                          null comment '对账结果说明',
    is_deleted       tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created          timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                                   null comment '创建人',
    last_modified    timestamp           default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by varchar(32)                                   null comment '修改人'
)
    comment '贵州按天对账信息表';

create index idx_chain_id
    on shebao_guizhou_settle_day (chain_id);

create index idx_clinic_id
    on shebao_guizhou_settle_day (clinic_id);

