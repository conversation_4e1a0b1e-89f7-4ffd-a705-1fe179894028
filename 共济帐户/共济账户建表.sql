-- 贵州共济账户
alter table shebao_guizhou_payment_result add out_extend json null comment '结算时输出的扩展信息';

alter table shebao_guizhou_payment_result add acct_mulaid_pay decimal(16, 2) null comment '账户共济下账金额';

alter table shebao_guizhou_payment_result add ykc299 varchar(8) null comment '城乡居民类别';

alter table shebao_guizhou_payment_result add ake183 decimal(16, 2) null comment '优抚补偿金额';

alter table shebao_guizhou_payment_result add yka082 decimal(16, 2) null comment '大额补充医疗报销金额';

alter table shebao_guizhou_refund_result add out_extend json null comment '结算时输出的扩展信息';

alter table shebao_guizhou_refund_result add acct_mulaid_pay decimal(16, 2) null comment '账户共济下账金额';

alter table shebao_guizhou_pre_payment_result add ykc299 varchar(8) null comment '城乡居民类别';

alter table shebao_guizhou_pre_payment_result add ake183 decimal(16, 2) null comment '优抚补偿金额';

alter table shebao_guizhou_pre_payment_result add yka082 decimal(16, 2) null comment '大额补充医疗报销金额';



create table shebao_guizhou_gongji_payment_result
(
    id                  bigint(64)                                    not null
        primary key,
    chain_id            varchar(32)                                   not null,
    clinic_id           varchar(32)                                   not null,
    task_id             varchar(32)                                   not null comment '交易任务id',
    region              varchar(32)                                   not null comment '刷卡地区',
    is_refunded         tinyint(1) unsigned default 0                 not null comment '是否已退费',
    payment_result_id   bigint(64)                                    not null comment '主payment_result_id',
    ykc173              varchar(256)                                  null comment '门诊诊断信息',
    auther_no           varchar(32)                                   null comment '账户共济授权人编码',
    acct_mulaid_pay     decimal(16, 2)                                null comment '账户共济下账金额',
    ykb065              varchar(8)                                    null comment '执行社会保险办法（险种）',
    aka130              varchar(8)                                    null comment '支付类别（码值：110102 名称：个人代支）',
    yka110              varchar(32)                                   null comment '发票号',
    aae013              varchar(256)                                  null comment '备注',
    aae011              varchar(32)                                   null comment '经办人',
    ykc141              varchar(64)                                   null comment '经办人姓名',
    ima_mdtrt_id        varchar(32)                                   null comment '原主交易就诊编号',
    ima_setl_id         varchar(32)                                   null comment '原主交易结算编号',
    ima_certno          varchar(64)                                   null comment '原主交易证件号码',
    ima_med_type        varchar(8)                                    null comment '原主交易支付类别',
    ima_medfee_sumamt   decimal(16, 2)                                null comment '原主交易医疗费总额',
    ima_fund_pay_sumamt decimal(16, 2)                                null comment '原主交易基金支付总额，原结算所有基金支出总和，不包含个人账户支出',
    ima_acct_pay        decimal(16, 2)                                null comment '原主交易个人账户支出',
    ima_psn_cash_pay    decimal(16, 2)                                null comment '原主交易个人现金支出',
    ima_psn_no          varchar(32)                                   null comment '原主交易个人编号',
    ima_psn_name        varchar(64)                                   null comment '原主交易人员姓名',
    user_insu_admdvs    varchar(64)                                   null comment '原主交易人员参保统筹区',
    akc190              varchar(32)                                   null comment '就诊编号',
    yab003              varchar(8)                                    null comment '分中心编号',
    yka103              varchar(32)                                   null comment '结算编号',
    aac001              varchar(32)                                   null comment '账户共济授权人编码',
    yka065              decimal(16, 2)                                null comment '个人账户支付金额',
    aae036              varchar(32)                                   null comment '经办时间',
    yka055              decimal(16, 2)                                null comment '医保结算费用总额',
    yka056              decimal(16, 2)                                null comment '全自费金额',
    yka057              decimal(16, 2)                                null comment '挂钩自付金额',
    yka111              decimal(16, 2)                                null comment '符合范围金额',
    yka058              decimal(16, 2)                                null comment '进入起付线金额',
    yka248              decimal(16, 2)                                null comment '基本医疗统筹支付金',
    yka062              decimal(16, 2)                                null comment '大额医疗支付金额 ',
    yke030              decimal(16, 2)                                null comment '公务员补助报销金额',
    ake032              decimal(16, 2)                                null comment '城乡居民卫计局补偿金额（卫计补偿）',
    ake181              decimal(16, 2)                                null comment '医疗救助补偿金额',
    ake173              decimal(16, 2)                                null comment '其他基金支付',
    ake183              decimal(16, 2)                                null comment '优抚补偿金额',
    akc087              decimal(16, 2)                                null comment '本次个人帐户支付后帐户余额',
    ykb037              varchar(8)                                    null comment '清算分中心',
    yka316              varchar(8)                                    null comment '清算类型',
    akc021              varchar(8)                                    null comment '医疗人员类型',
    ykc120              varchar(8)                                    null comment '公务员级别',
    yab139              varchar(8)                                    null comment '参保所属分中心',
    aac003              varchar(64)                                   null comment '姓名',
    aac004              varchar(8)                                    null comment '性别 1男 2女 9无',
    aac002              varchar(64)                                   null comment '公民身份证号',
    aac006              varchar(32)                                   null comment '出生日期',
    akc023              decimal(4, 1)                                 null comment '实足年龄',
    aab001              varchar(64)                                   null comment '单位编码',
    aab004              varchar(640)                                  null comment '单位名称',
    aac031              varchar(8)                                    null comment '个人参保状态',
    ykc280              varchar(8)                                    null comment '居民医疗人员类别',
    ykc281              varchar(8)                                    null comment '居民医疗人员身份',
    ykc299              varchar(8)                                    null comment '城乡居民人员类别',
    yka054              varchar(8)                                    null comment '清算方式',
    yae366              varchar(8)                                    null comment '清算期号',
    akc090              decimal(6)                                    null comment '本年真实住院次数',
    yka120              decimal(16, 2)                                null comment '基本统筹已累计金额',
    yka122              decimal(16, 2)                                null comment '大额统筹已累计金额',
    yka368              decimal(16, 2)                                null comment '公务员补助普通门诊起付年度累计(含慢性病)',
    yke025              decimal(16, 2)                                null comment '本年公务员门诊补助累计额(含慢性病)',
    yka900              decimal(16, 2)                                null comment '规定病种起付线累计',
    aae001              decimal(4)                                    null comment '年度',
    yka082              decimal(16, 2)                                null comment '大额补充保险报销金额',
    yka083              decimal(16, 2)                                null comment '大病额外报销金额',
    yka084              decimal(16, 2)                                null comment '公务员额外报销金额',
    yka085              decimal(16, 2)                                null comment '工会额外报销金额',
    yka086              decimal(16, 2)                                null comment '尽快自付金额',
    yka087              decimal(16, 2)                                null comment '诊疗超标自付金额',
    yka088              decimal(16, 2)                                null comment '床位超标自付金额',
    yka090              decimal(16, 2)                                null comment '限价材料超标自付金额',
    yka089              varchar(32)                                   null comment '单病种(结算)编码',
    yka027              varchar(640)                                  null comment '单病种(结算)病种名称',
    yka028              decimal(16, 2)                                null comment '单病种(结算)医疗机构自费费用',
    yka345              decimal(16, 2)                                null comment '单病种(结算)包干标准',
    yka525              decimal(16, 2)                                null comment '医疗结算费用总,贵阳市医保接口返回',
    yka501              varchar(64)                                   null comment '门诊产前补助',
    ykd092              varchar(64)                                   null comment '重大疾病标识',
    yka902              varchar(64)                                   null comment '慢性病门诊预设线累计',
    yka903              varchar(64)                                   null comment '慢性病门诊补助年度累计',
    yka119              varchar(64)                                   null comment '已使用额度',
    psn_cash_pay        decimal(14, 2)                                null comment '本次现金支付',
    hosp_part_amt       decimal(14, 2)                                null comment '医院承担',
    jylsh               varchar(32)                                   null comment '交易流水号',
    jyyzm               varchar(64)                                   null comment '交易验证码',
    stmt_rslt           varchar(8)                                    null comment '和社保的对账结果，0=平 1=不平 101=中心多 102=机构多 103=数据不一致',
    stmt_rslt_memo      text                                          null comment '和社保的对账结果失败后说明',
    is_deleted          tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created             timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by          varchar(32)                                   not null comment '创建人',
    last_modified_by    varchar(32)                                   not null comment '最后修改人',
    last_modified       timestamp           default CURRENT_TIMESTAMP not null comment '最后修改时间'
)
    comment '贵州账户共济结算';

create index idx_chain_id
    on shebao_guizhou_gongji_payment_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_gongji_payment_result (clinic_id);

create index idx_ima_mdtrt_id
    on shebao_guizhou_gongji_payment_result (ima_mdtrt_id);

create index idx_payment_result_id
    on shebao_guizhou_gongji_payment_result (payment_result_id);

create index idx_task_id
    on shebao_guizhou_gongji_payment_result (task_id);


create table shebao_guizhou_gongji_pre_payment_result
(
    id                  bigint(64)                                    not null
        primary key,
    chain_id            varchar(32)                                   not null,
    clinic_id           varchar(32)                                   not null,
    task_id             varchar(32)                                   not null comment '交易任务id',
    region              varchar(32)                                   not null comment '刷卡地区',
    ykc173              varchar(256)                                  null comment '门诊诊断信息',
    auther_no           varchar(32)                                   null comment '账户共济授权人编码',
    acct_mulaid_pay     decimal(16, 2)                                null comment '账户共济下账金额',
    ykb065              varchar(8)                                    null comment '执行社会保险办法（险种）',
    aka130              varchar(8)                                    null comment '支付类别（码值：110102 名称：个人代支）',
    yka110              varchar(32)                                   null comment '发票号',
    aae013              varchar(256)                                  null comment '备注',
    aae011              varchar(32)                                   null comment '经办人',
    ykc141              varchar(64)                                   null comment '经办人姓名',
    ima_mdtrt_id        varchar(32)                                   null comment '原主交易就诊编号',
    ima_setl_id         varchar(32)                                   null comment '原主交易结算编号',
    ima_certno          varchar(64)                                   null comment '原主交易证件号码',
    ima_med_type        varchar(8)                                    null comment '原主交易支付类别',
    ima_medfee_sumamt   decimal(16, 2)                                null comment '原主交易医疗费总额',
    ima_fund_pay_sumamt decimal(16, 2)                                null comment '原主交易基金支付总额，原结算所有基金支出总和，不包含个人账户支出',
    ima_acct_pay        decimal(16, 2)                                null comment '原主交易个人账户支出',
    ima_psn_cash_pay    decimal(16, 2)                                null comment '原主交易个人现金支出',
    ima_psn_no          varchar(32)                                   null comment '原主交易个人编号',
    ima_psn_name        varchar(64)                                   null comment '原主交易人员姓名',
    user_insu_admdvs    varchar(64)                                   null comment '原主交易人员参保统筹区',
    akc190              varchar(32)                                   null comment '就诊编号',
    yab003              varchar(8)                                    null comment '分中心编号',
    yka103              varchar(32)                                   null comment '结算编号',
    aac001              varchar(32)                                   null comment '账户共济授权人编码',
    yka065              decimal(16, 2)                                null comment '个人账户支付金额',
    aae036              varchar(32)                                   null comment '经办时间',
    yka055              decimal(16, 2)                                null comment '医保结算费用总额',
    yka056              decimal(16, 2)                                null comment '全自费金额',
    yka057              decimal(16, 2)                                null comment '挂钩自付金额',
    yka111              decimal(16, 2)                                null comment '符合范围金额',
    yka058              decimal(16, 2)                                null comment '进入起付线金额',
    yka248              decimal(16, 2)                                null comment '基本医疗统筹支付金',
    yka062              decimal(16, 2)                                null comment '大额医疗支付金额 ',
    yke030              decimal(16, 2)                                null comment '公务员补助报销金额',
    ake032              decimal(16, 2)                                null comment '城乡居民卫计局补偿金额（卫计补偿）',
    ake181              decimal(16, 2)                                null comment '医疗救助补偿金额',
    ake173              decimal(16, 2)                                null comment '其他基金支付',
    ake183              decimal(16, 2)                                null comment '优抚补偿金额',
    akc087              decimal(16, 2)                                null comment '本次个人帐户支付后帐户余额',
    ykb037              varchar(8)                                    null comment '清算分中心',
    yka316              varchar(8)                                    null comment '清算类型',
    akc021              varchar(8)                                    null comment '医疗人员类型',
    ykc120              varchar(8)                                    null comment '公务员级别',
    yab139              varchar(8)                                    null comment '参保所属分中心',
    aac003              varchar(64)                                   null comment '姓名',
    aac004              varchar(8)                                    null comment '性别 1男 2女 9无',
    aac002              varchar(64)                                   null comment '公民身份证号',
    aac006              varchar(32)                                   null comment '出生日期',
    akc023              decimal(4, 1)                                 null comment '实足年龄',
    aab001              varchar(64)                                   null comment '单位编码',
    aab004              varchar(640)                                  null comment '单位名称',
    aac031              varchar(8)                                    null comment '个人参保状态',
    ykc280              varchar(8)                                    null comment '居民医疗人员类别',
    ykc281              varchar(8)                                    null comment '居民医疗人员身份',
    ykc299              varchar(8)                                    null comment '城乡居民人员类别',
    yka054              varchar(8)                                    null comment '清算方式',
    yae366              varchar(8)                                    null comment '清算期号',
    akc090              decimal(6)                                    null comment '本年真实住院次数',
    yka120              decimal(16, 2)                                null comment '基本统筹已累计金额',
    yka122              decimal(16, 2)                                null comment '大额统筹已累计金额',
    yka368              decimal(16, 2)                                null comment '公务员补助普通门诊起付年度累计(含慢性病)',
    yke025              decimal(16, 2)                                null comment '本年公务员门诊补助累计额(含慢性病)',
    yka900              decimal(16, 2)                                null comment '规定病种起付线累计',
    aae001              decimal(4)                                    null comment '年度',
    yka082              decimal(16, 2)                                null comment '大额补充保险报销金额',
    yka083              decimal(16, 2)                                null comment '大病额外报销金额',
    yka084              decimal(16, 2)                                null comment '公务员额外报销金额',
    yka085              decimal(16, 2)                                null comment '工会额外报销金额',
    yka086              decimal(16, 2)                                null comment '尽快自付金额',
    yka087              decimal(16, 2)                                null comment '诊疗超标自付金额',
    yka088              decimal(16, 2)                                null comment '床位超标自付金额',
    yka090              decimal(16, 2)                                null comment '限价材料超标自付金额',
    yka089              varchar(32)                                   null comment '单病种(结算)编码',
    yka027              varchar(640)                                  null comment '单病种(结算)病种名称',
    yka028              decimal(16, 2)                                null comment '单病种(结算)医疗机构自费费用',
    yka345              decimal(16, 2)                                null comment '单病种(结算)包干标准',
    yka525              decimal(16, 2)                                null comment '医疗结算费用总,贵阳市医保接口返回',
    yka501              varchar(64)                                   null comment '门诊产前补助',
    ykd092              varchar(64)                                   null comment '重大疾病标识',
    yka902              varchar(64)                                   null comment '慢性病门诊预设线累计',
    yka903              varchar(64)                                   null comment '慢性病门诊补助年度累计',
    yka119              varchar(64)                                   null comment '已使用额度',
    psn_cash_pay        decimal(14, 2)                                null comment '本次现金支付',
    hosp_part_amt       decimal(14, 2)                                null comment '医院承担',
    jylsh               varchar(32)                                   null comment '交易流水号',
    jyyzm               varchar(64)                                   null comment '交易验证码',
    is_cancelled        tinyint(1) unsigned default 0                 null comment '是否取消了预支付 0未取消, 1取消',
    msgid               varchar(64)                                   null comment '对账时返回msgid,用于冲正',
    has_reversed        tinyint(1) unsigned default 0                 not null comment '是否已经冲正过 0未冲正 1已冲正',
    reversed_time       varchar(32)                                   null comment '冲正时间',
    is_deleted          tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created             timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by          varchar(32)                                   not null comment '创建人',
    last_modified_by    varchar(32)                                   not null comment '最后修改人',
    last_modified       timestamp           default CURRENT_TIMESTAMP not null comment '最后修改时间'
)
    comment '贵州账户共济预结算';

create index idx_chain_id
    on shebao_guizhou_gongji_pre_payment_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_gongji_pre_payment_result (clinic_id);

create index idx_ima_mdtrt_id
    on shebao_guizhou_gongji_pre_payment_result (ima_mdtrt_id);

create index idx_task_id
    on shebao_guizhou_gongji_pre_payment_result (task_id);


create table shebao_guizhou_gongji_pre_refund_result
(
    id                bigint(64)                           not null
        primary key,
    chain_id          varchar(64)                          null comment '连锁id',
    clinic_id         varchar(64)                          null comment '诊所id',
    task_id           varchar(64)                          null comment '交易任务id',
    region            varchar(64)                          null comment '区域',
    payment_result_id bigint(64)                           null comment '共济结算payment_result_id',
    akc190            varchar(64)                          null comment '就诊编号',
    yab003            varchar(8)                           null comment '分中心编码',
    aka130            varchar(8)                           null comment '支付类别',
    yka103            varchar(64)                          null comment '结算编号',
    aae011            varchar(128)                         null comment '经办人编码',
    ykc141            varchar(64)                          null comment '经办人姓名',
    aae036            varchar(32)                          null comment '经办时间',
    aae013            varchar(256)                         null comment '退费原因',
    ykb065            varchar(8)                           null comment '社会保险办法',
    aac001            varchar(64)                          null comment '个人编码',
    jylsh             varchar(64)                          null comment '交易流水号',
    jyyzm             varchar(32)                          null comment '交易验证码',
    ojylsh            varchar(64)                          null comment '结算交易流水号',
    is_canceled       tinyint(1) default 0                 not null comment '预退费是否关闭',
    has_reversed      tinyint(1) default 0                 not null comment '是否已经冲正过 0未冲正 1已冲正',
    reversed_time     varchar(32)                          null comment '冲正时间',
    is_deleted        tinyint(1) default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created           timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by        varchar(64)                          null comment '创建人',
    last_modified     timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by  varchar(64)                          null comment '修改人'
)
    comment '贵州共济帐户预退费表';

create index idx_chain_id
    on shebao_guizhou_gongji_pre_refund_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_gongji_pre_refund_result (clinic_id);

create index idx_task_id
    on shebao_guizhou_gongji_pre_refund_result (task_id);

create index idx_yka103
    on shebao_guizhou_gongji_pre_refund_result (yka103);


create table shebao_guizhou_gongji_refund_result
(
    id                bigint(64)                           not null
        primary key,
    chain_id          varchar(64)                          null comment '连锁id',
    clinic_id         varchar(64)                          null comment '诊所id',
    task_id           varchar(64)                          null comment '交易任务id',
    region            varchar(64)                          null comment '区域',
    payment_result_id bigint(64)                           null comment '共济结算payment_result_id',
    akc190            varchar(64)                          null comment '就诊编号',
    yab003            varchar(8)                           null comment '分中心编码',
    aka130            varchar(8)                           null comment '支付类别',
    yka103            varchar(64)                          null comment '结算编号',
    aae011            varchar(128)                         null comment '经办人编码',
    ykc141            varchar(64)                          null comment '经办人姓名',
    aae036            varchar(32)                          null comment '经办时间',
    aae013            varchar(256)                         null comment '退费原因',
    ykb065            varchar(8)                           null comment '社会保险办法',
    aac001            varchar(64)                          null comment '个人编码',
    jylsh             varchar(64)                          null comment '交易流水号',
    jyyzm             varchar(32)                          null comment '交易验证码',
    yka198            varchar(64)                          null comment '退费结算编号',
    stmt_rslt         varchar(8)                           null comment '和社保的对账结果，0=平 1=不平 101=中心多 102=机构多 103=数据不一致',
    stmt_rslt_memo    text                                 null comment '和社保的对账结果失败后说明',
    is_deleted        tinyint(1) default 0                 not null comment '删除状态 0：未删除；1：已删除',
    created           timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by        varchar(64)                          null comment '创建人',
    last_modified     timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by  varchar(64)                          null comment '修改人'
)
    comment '贵州共济账户退费表';

create index idx_chain_id
    on shebao_guizhou_gongji_refund_result (chain_id);

create index idx_clinic_id
    on shebao_guizhou_gongji_refund_result (clinic_id);

create index idx_task_id
    on shebao_guizhou_gongji_refund_result (task_id);

create index idx_yka103
    on shebao_guizhou_gongji_refund_result (yka103);