-- 中药
select /*+ max_execution_time(3600000)*/
    id                                     as id,
    region                                 as region,
    if(yka284 is not null, yka284, yka002) as shebao_code,
    yka003                                 as name,
    null                                   as used_part,
    null                                   as method
from shebao_guizhou_dict
where yka001 = '10' or (yka001 = '11' and yka284 like 'T%');

-- select * from shebao_national_dict_drug_tcm where region = 'guizhou_guiyang';

-- 西药/中成药
select /*+ max_execution_time(3600000)*/
    id                                     as id,
    region                                 as region,
    if(yka284 is not null, yka284, yka002) as shebao_code,
    left(yka609, 128)                      as trade_name,
    null                                   as cadn_code,
    left(yka003, 128)                      as cadn,
    null                                   as chemical_name,
    null                                   as alias,
    null                                   as english_name,
    left(yka003, 128)                      as registered_name,
    null                                   as admin_standard_code,
    null                                   as dosage_form_code,
    aka070                                 as dosage_form_name,
    null                                   as category_code,
    null                                   as category_name,
    aka074                                 as spec_name,
    null                                   as spec_code,
    null                                   as registered_dosage_form,
    null                                   as registered_spec_name,
    null                                   as registered_spec_code,
    null                                   as dosage,
    null                                   as freq,
    null                                   as radical,
    null                                   as national_code,
    null                                   as usage2,
    null                                   as chinese_medicine_flag,
    null                                   as produce_category_code,
    null                                   as produce_category_name,
    null                                   as sales_unit,
    null                                   as otc_flag,
    null                                   as otc_name,
    null                                   as pack_material_code,
    null                                   as pack_material_name,
    null                                   as pack_spec,
    null                                   as pack_num,
    null                                   as indications,
    null                                   as route,
    null                                   as introduce,
    null                                   as begin_date,
    null                                   as end_date,
    null                                   as smallest_usage_unit,
    null                                   as smallest_sales_unit,
    null                                   as smallest_dosage_unit,
    null                                   as smallest_pack_num,
    null                                   as smallest_pack_unit,
    null                                   as smallest_dosage_form_unit,
    null                                   as smallest_pack_unit_name,
    null                                   as smallest_dosage_form_unit_name,
    null                                   as conversion_ratio,
    null                                   as expire_date,
    yka295                                 as smallest_price_unit,
    yka401                                 as wubi,
    yka389                                 as pinyin,
    null                                   as pack_separate_manufacture,
    null                                   as manufacture_code,
    yka601                                 as manufacture_name,
    null                                   as unusual_price_limited_flag,
    null                                   as unusual_flag,
    null                                   as restricted_range,
    null                                   as restricted_flag,
    null                                   as registered_no,
    null                                   as registered_no_begin_date,
    null                                   as registered_no_end_date,
    left(yka007, 64)                       as approved_code,
    null                                   as approved_begin_date,
    null                                   as approved_end_date,
    null                                   as market_status,
    null                                   as market_status_name,
    null                                   as archive,
    null                                   as archive_ext,
    null                                   as national_remark,
    null                                   as base_flag_name,
    null                                   as base_flag,
    null                                   as tax_flag,
    null                                   as tax_flag_name,
    null                                   as publish,
    null                                   as nego_flag,
    null                                   as nego_name,
    null                                   as heath_committee_code,
    null                                   as remark,
    null                                   as valid_flag,
    null                                   as record_no,
    null                                   as created_time,
    yae036                                 as updatedTime,
    null                                   as version,
    null                                   as version_name,
    null                                   as child,
    null                                   as company,
    null                                   as copy_consistency,
    null                                   as broker_company,
    null                                   as broker_contact,
    null                                   as broker_archive,
    null                                   as national_dosage_form,
    case
        when ykd110 = '01' then '甲'
        when ykd110 = '02' then '乙'
        when ykd110 = '03' then '自费'
        end                                as national_fee_type
from shebao_guizhou_dict
where (yka001 = '09' or (yka001 = '11' and yka284 not like 'T%'))
  and ykd110 in ('01', '02', '03');

-- select * from shebao_national_dict_drug where region = 'guizhou_guiyang';

-- 材料
select /*+ max_execution_time(3600000)*/
    id                                     as id,
    region                                 as region,
    if(yka284 is not null, yka284, yka002) as shebao_code,
    yka003                                 as name,
    aka074                                 as gui_ge,
    yka601                                 as sheng_chan_qi_ye_ming_cheng
from shebao_guizhou_dict
where yka001 = '08';
select * from shebao_guizhou_dict;
-- select * from shebao_national_dict_material where region = 'guizhou_guiyang';

-- 项目
select /*+ max_execution_time(3600000)*/
    id                                     as id,
    region                                 as region,
    if(yka284 is not null, yka284, yka002) as shebao_code,
    yka003                                 as name,
    null                                   as unit_code,
    yka295                                 as unit_name,
    yke103                                 as detail,
    yke054                                 as exception,
    yke100                                 as include,
    null                                   as valid_flag,
    aae013                                 as remark,
    null                                   as category,
    yke103                                 as description,
    null                                   as begin_date,
    null                                   as end_date,
    null                                   as record_no,
    null                                   as version,
    null                                   as version_name
from shebao_guizhou_dict
where yka001 not in ('08', '09', '10', '11');

-- select * from shebao_national_dict_treatment where region='guizhou_guiyang';