-- 查询结算列表
select a.id                                                         id,
       a.paid_task_id                                               paidTaskId,
       a.patient_name                                               patient<PERSON><PERSON>,
       a.doctor_name                                                doctor<PERSON>ame,
       a.type                                                       type,
       if(a.pay_status in (2, 3), a.received_fee, a.receivable_fee) payFee,
       a.pay_status                                                 payStatus,
       a.payment_result_type                                        businessType,
       ifnull(a.charged_time, a.created)                            chargedTime,
       a.created_by                                                 chargedById,
       b.yka103                                                     transactionId,
       b.is_refunded                                                isRefunded,
       b.card_id                                                    cardId,
       b.aac002                                                     idCardNo,
       b.aac003                                                     cardOwner,
       b.relation_to_patient                                        relationToPatient,
       b.yka103                                                     yka103
from shebao_pay_task a
         join shebao_guizhou_payment_result b
              on a.payment_result_id = b.id and a.chain_id = b.chain_id and a.clinic_id = b.clinic_id and
                 b.is_deleted = 0
         left join shebao_guizhou_refund_result c
                   on c.id = a.refund_result_id and a.clinic_id = c.clinic_id and a.chain_id = c.chain_id and
                      c.is_deleted = 0
where a.chain_id = 'ffffffff000000000c5a1308069aa000'
  and a.clinic_id = 'ffffffff0000000021c580c8011b0000'
  and a.is_deleted = 0
  and (a.pay_status in (2, 3) or a.is_pre_refund = 1)
  and ifnull(a.charged_time, a.created) > '2022-04-14 00:00:00'
  and ifnull(a.charged_time, a.created) < '2022-04-14 23:59:59'
order by ifnull(a.charged_time, a.created) desc
limit 0, 10;

-- 查询结算总数和金额
select count(*)                                                         totalCount,
       sum(if(a.type = 0 and a.pay_status = 2, a.received_fee, 0))      settleAmount,
       abs(sum(if(a.type = 1 and a.pay_status = 2, a.received_fee, 0))) refundAmount
from shebao_pay_task a
         join shebao_guizhou_payment_result b
              on a.payment_result_id = b.id and a.chain_id = b.chain_id and b.clinic_id = a.clinic_id and
                 b.is_deleted = 0
         left join shebao_guizhou_refund_result c
                   on c.id = a.refund_result_id and a.clinic_id = b.clinic_id and a.chain_id = c.chain_id and
                      c.is_deleted = 0
where a.chain_id = 'ffffffff000000000c5a1308069aa000'
  and a.clinic_id = 'ffffffff0000000021c580c8011b0000'
  and a.is_deleted = 0
  and (a.pay_status in (2, 3) or a.is_pre_refund = 1)
  and ifnull(a.charged_time, a.created) > '2022-04-14 00:00:00'
  and ifnull(a.charged_time, a.created) < '2022-04-14 23:59:59';

-- 所有结算单
select a.pay_status                                         payStatus,
       a.type                                               type,
       a.payment_result_id                                  payTaskPaymentResultId,
       c.id                                                 paymentResultId,
       b.id                                                 prePaymentResultId,
       a.received_fee                                       receivedFee,
       c.jylsh                                              jylsh,
       b.yka103                                             preYka103,
       if(c.id is not null, c.yka103, b.yka103)             yka103,
       b.msgid                                              msgid,
       if(c.id is not null, c.akc190, b.akc190)             akc190,
       if(c.id is not null, c.yka316, b.yka316)             yka316,
       if(c.id is not null, c.yka054, b.yka054)             yka054,
       if(c.id is not null, c.ykb065, b.ykb065)             ykb065,
       if(c.id is not null, c.ykb037, b.ykb037)             ykb037,
       if(c.id is not null, c.aac001, b.aac001)             aac001,
       if(c.id is not null, c.aac003, b.aac003)             aac003,
       a.patient_name                                       patientName,
       b.created                                            created,
       c.is_refunded                                        isRefunded,
       c.stmt_rslt                                          stmtRslt,
       if(c.id is not null, c.yka055, b.yka055)             yka055,
       if(c.id is not null, c.yka248, b.yka248)             yka248,
       if(c.id is not null, c.yka065, b.yka065)             yka065,
       if(c.id is not null, c.psn_cash_pay, b.psn_cash_pay) psnCashPay,
       c.stmt_rslt_memo                                     stmtRsltMemo,
       b.has_reversed                                       hasReversed
from shebao_pay_task a
    join shebao_guizhou_pre_payment_result b on a.id = b.task_id and a.clinic_id = b.clinic_id and
                                                b.is_deleted = 0
    left join shebao_guizhou_payment_result c
        on c.akc190 = b.akc190 and c.task_id = b.task_id and c.clinic_id = b.clinic_id and c.is_deleted = 0
where a.type = 0 and b.is_cancelled != 1
    and a.chain_id = 'ffffffff000000000c5a1308069aa000' and b.clinic_id = 'ffffffff0000000021c580c8011b0000'
    and b.created > '2022-04-14 00:00:00' and b.created < '2022-04-14 23:59:59';

-- 所有退费单
select
    a.id              taskId,
    c.id              refundResultId,
    d.id              preRefundResultId,
    a.pay_status      payStatus,
    a.type            type,
    b.jylsh           jylsh,
    d.jylsh           refundJylsh,
    d.msgid           refundMsgid,
    a.received_fee    receivedFee,
    b.yka103          paidYka103,
    b.ykb065          ykb065,
    b.yka316          yka316,
    b.yka054          yka054,
    b.is_refunded     isRefunded,
    b.ykb037          ykb037,
    if(c.id is not null, c.yka103, b.yka103)          yka103,
    d.yka103          preYka103,
    b.akc190          akc190,
    b.aac001          aac001,
    b.aac003          aac003,
    a.patient_name    patientName,
    a.pre_refund_time preRefundTime,
    -b.yka055         yka055,
    -b.yka248         yka248,
    -b.yka065         yka065,
    b.created         created,
    c.stmt_rslt       stmtRslt,
    -b.psn_cash_pay   psnCashPay,
    c.stmt_rslt_memo  stmtRsltMemo,
    b.yab003          yab003,
    b.aka130          aka130,
    b.aae011          aae011,
    b.ykc141          ykc141,
    b.aae013          aae013
from shebao_pay_task a
    join shebao_guizhou_payment_result b
        on b.id = a.payment_result_id and a.clinic_id = b.clinic_id and b.is_deleted = 0
    join shebao_guizhou_pre_refund_result d
        on a.chain_id = d.chain_id and a.id = d.task_id and d.is_canceled != 1 and d.is_deleted = 0
    left join shebao_guizhou_refund_result c
        on c.id = a.refund_result_id and c.clinic_id = a.clinic_id and c.is_deleted = 0 and c.task_id = d.task_id
where a.chain_id = 'ffffffff000000000c5a1308069aa000' and a.clinic_id = 'ffffffff0000000021c580c8011b0000' and a.is_deleted = 0 and a.type = 1
    and (a.is_pre_refund = 1 or a.pay_status in (2, 3) or d.id is not null)
    and a.pre_refund_time > '2022-04-14 00:00:00' and a.pre_refund_time < '2022-04-14 23:59:59';


-- 所有结算成功
explain select a.pay_status                                         payStatus,
       a.type                                               type,
       a.payment_result_id                                  payTaskPaymentResultId,
       c.id                                                 paymentResultId,
       b.id                                                 prePaymentResultId,
       a.received_fee                                       receivedFee,
       c.jylsh                                              jylsh,
       b.yka103                                             preYka103,
       c.yka103                                             yka103,
       b.msgid                                              msgid,
       if(c.id is not null, c.akc190, b.akc190)             akc190,
       if(c.id is not null, c.yka316, b.yka316)             yka316,
       if(c.id is not null, c.yka054, b.yka054)             yka054,
       if(c.id is not null, c.ykb065, b.ykb065)             ykb065,
       if(c.id is not null, c.ykb037, b.ykb037)             ykb037,
       if(c.id is not null, c.aac001, b.aac001)             aac001,
       if(c.id is not null, c.aac003, b.aac003)             aac003,
       a.patient_name                                       patientName,
       b.created                                            created,
       c.is_refunded                                        isRefunded,
       c.stmt_rslt                                          stmtRslt,
       if(c.id is not null, c.yka055, b.yka055)             yka055,
       if(c.id is not null, c.yka248, b.yka248)             yka248,
       if(c.id is not null, c.yka065, b.yka065)             yka065,
       if(c.id is not null, c.psn_cash_pay, b.psn_cash_pay) psnCashPay,
       c.stmt_rslt_memo                                     stmtRsltMemo,
       b.has_reversed                                       hasReversed
from shebao_pay_task a
         join shebao_guizhou_pre_payment_result b on a.id = b.task_id and a.clinic_id = b.clinic_id and
                                                     b.is_deleted = 0
         join shebao_guizhou_payment_result c
              on c.akc190 = b.akc190 and c.task_id = b.task_id and c.clinic_id = b.clinic_id and c.is_deleted = 0
where a.type = 0
  and b.is_cancelled != 1
  and a.chain_id = 'ffffffff000000000c5a1308069aa000'
  and b.clinic_id = 'ffffffff0000000021c580c8011b0000'
  and b.created > '2022-04-01 00:00:00'
  and b.created < '2022-04-30 23:59:59' and b.yae366 = '202204';


-- 退费成功
select
    c.id              refundResultId,
    d.id              preRefundResultId,
    a.pay_status      payStatus,
    a.type            type,
    b.jylsh           jylsh,
    d.jylsh           refundJylsh,
    d.msgid           refundMsgid,
    a.received_fee    receivedFee,
    b.yka103          paidYka103,
    b.ykb065          ykb065,
    b.yka316          yka316,
    b.yka054          yka054,
    b.is_refunded     isRefunded,
    b.ykb037          ykb037,
    c.yka103          yka103,
    d.yka103          preYka103,
    b.akc190          akc190,
    b.aac001          aac001,
    b.aac003          aac003,
    a.patient_name    patientName,
    a.pre_refund_time preRefundTime,
    -b.yka055         yka055,
    -b.yka248         yka248,
    -b.yka065         yka065,
    b.created         created,
    c.stmt_rslt       stmtRslt,
    -b.psn_cash_pay   psnCashPay,
    c.stmt_rslt_memo  stmtRsltMemo
from shebao_pay_task a
         join shebao_guizhou_payment_result b
              on b.id = a.payment_result_id and a.clinic_id = b.clinic_id and b.is_deleted = 0
         join shebao_guizhou_refund_result c
                   on c.id = a.refund_result_id and c.clinic_id = a.clinic_id and c.is_deleted = 0
         join shebao_guizhou_pre_refund_result d
                   on a.chain_id = d.chain_id and a.id = d.task_id and d.is_canceled != 1 and d.is_deleted = 0 and c.task_id = d.task_id
where a.chain_id = 'ffffffff000000000c5a1308069aa000' and a.clinic_id = 'ffffffff0000000021c580c8011b0000' and a.is_deleted = 0 and a.type = 1
  and (a.is_pre_refund = 1 or a.pay_status = 2)
  and a.pre_refund_time > '2022-04-14 00:00:00' and a.pre_refund_time < '2022-04-14 23:59:59';

-- 待冲正交易
select a.type          type,
       a.id            taskId,
       a.pay_status    payStatus,
       b.id            prePaymentResultId,
       c.id            paymentResultId,
       b.msgid         preMsgid,
       c.yka103        yka103,
       b.has_reversed  hasReversed,
       b.reversed_time reversedTime
from shebao_pay_task a
         inner join shebao_guizhou_pre_payment_result b
                    on a.id = b.task_id and a.clinic_id = b.clinic_id and b.is_deleted = 0
         left join shebao_guizhou_payment_result c
                   on c.task_id = b.task_id and a.clinic_id = c.clinic_id and c.is_deleted = 0 and b.yka103 = c.yka103
where a.type = 0
  and a.is_deleted = 0
  and b.is_cancelled != 1
  and a.clinic_id = 'ffffffff0000000021c580c8011b0000'
  and b.msgid = 'H52010200024202204141501516672';