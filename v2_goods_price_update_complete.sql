-- 基于提供的SQL语句补全的价格更新脚本
-- 原始语句：update v2_goods set piece_price = 14.223, package_price = 14.223 where organ_id = 'ffffffff00000000280fbf880b580000' and short_id = '70701912';

-- 1. 查询当前价格信息（更新前确认）
SELECT organ_id, short_id, piece_price, package_price, 
       CASE WHEN piece_price != 14.223 OR package_price != 14.223 THEN '需要更新' ELSE '价格已正确' END as status
FROM v2_goods 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 2. 原始更新语句
UPDATE v2_goods 
SET piece_price = 14.223, package_price = 14.223 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 3. 验证更新结果
SELECT organ_id, short_id, piece_price, package_price, 
       CASE WHEN piece_price = 14.223 AND package_price = 14.223 THEN '更新成功' ELSE '更新失败' END as result
FROM v2_goods 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 4. 如果需要批量更新同一机构下的多个商品（请根据实际需求调整short_id列表）
UPDATE v2_goods 
SET piece_price = 14.223, package_price = 14.223 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' 
AND short_id IN ('70701912', '70701913', '70701914'); -- 请根据实际需要的商品ID调整

-- 5. 如果需要更新同一商品在不同机构的价格（请根据实际需求调整organ_id列表）
UPDATE v2_goods 
SET piece_price = 14.223, package_price = 14.223 
WHERE short_id = '70701912' 
AND organ_id IN ('ffffffff00000000280fbf880b580000', 'ffffffff00000000280fbf880b580001'); -- 请根据实际机构ID调整

-- 6. 备份当前数据（建议在大批量更新前执行）
CREATE TABLE v2_goods_backup_20241212 AS 
SELECT * FROM v2_goods 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 7. 查询相同价格的其他商品（用于价格一致性检查）
SELECT organ_id, short_id, piece_price, package_price
FROM v2_goods 
WHERE (piece_price = 14.223 OR package_price = 14.223)
AND organ_id = 'ffffffff00000000280fbf880b580000'
ORDER BY short_id;

-- 8. 价格历史记录（如果有价格变更日志表的话）
-- INSERT INTO v2_goods_price_history (organ_id, short_id, old_piece_price, old_package_price, new_piece_price, new_package_price, update_time, update_by)
-- SELECT organ_id, short_id, piece_price, package_price, 14.223, 14.223, NOW(), 'system'
-- FROM v2_goods 
-- WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912'
-- AND (piece_price != 14.223 OR package_price != 14.223);

-- 9. 事务版本（确保数据一致性）
START TRANSACTION;

-- 更新前检查
SELECT COUNT(*) as record_count FROM v2_goods 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 执行更新
UPDATE v2_goods 
SET piece_price = 14.223, package_price = 14.223 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 验证更新
SELECT piece_price, package_price FROM v2_goods 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' AND short_id = '70701912';

-- 如果结果正确则提交，否则回滚
-- COMMIT;
-- ROLLBACK;

-- 10. 条件更新（只在价格不同时才更新，避免不必要的写操作）
UPDATE v2_goods 
SET piece_price = 14.223, package_price = 14.223 
WHERE organ_id = 'ffffffff00000000280fbf880b580000' 
AND short_id = '70701912'
AND (piece_price != 14.223 OR package_price != 14.223);
